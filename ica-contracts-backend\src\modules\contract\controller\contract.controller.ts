import {
  Body,
  Controller,
  Get,

  HttpCode,
  HttpException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Request,

  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import {
  ApiOperation,
  ApiOkResponse,
  ApiBearerAuth,
  ApiResponse,
  ApiBody,
  ApiTags,
} from '@nestjs/swagger';
import { ApiSortingQuery } from 'src/modules/contract/decorators/sorting-params-swagger.decorator';
import {
  SortingParams,
  SortingParam,
} from 'src/modules/contract/decorators/sorting-params.decorator';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';
import { RoleGuard } from 'src/shared/guards/role.guard';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';

import { AddSignatariesDto } from '../dto/add-signataries.dto';
import { CreateContractAdditiveManualDto } from '../dto/create-contract-additive-manual.dto';
import { CreateContractAdditiveDto } from '../dto/create-contract-additive.dto';
import { CreateNewContractDto } from '../dto/create-contract-manual.dto';
import { CreateContractDto } from '../dto/create-contract.dto';
import { ContractIdParamDto } from '../dto/delete-contract-params.dto';
import { DeleteContractResponseDto } from '../dto/delete-contract-response.dto';
import { DeleteContractDto } from '../dto/delete-contract.dto';
import { EditContractParamsDto } from '../dto/edit-contract-params.dto';
import { EditNewContractDto } from '../dto/edit-new-contract.dto';
import { GetContractsResponse } from '../dto/get-contracts-response.dto';
import { GetContractsDto } from '../dto/get-contracts.dto';
import { GetContractDto } from '../dto/get-one-contract.dto';
import { ListContractsSuperadminDto } from '../dto/list-contracts-superadmin.dto';
import { RenewContractDto } from '../dto/renew-contract.dto';
import { UploadProofPaymentAddendumDto } from '../dto/upload-proof-payment-addendum.dto';
import { UploadProofPaymentDto } from '../dto/upload-proof-payment.dto';
import { ContractSortFieldEnum } from '../enums/contract-sort.enum';
import { AddSignatoriesService } from '../services/add-signataries.service';
import { CreateContractAdditiveManualService } from '../services/create-contract-additive-manual.service';
import { CreateContractAdditiveService } from '../services/create-contract-additive.service';
import { CreateContractManualService } from '../services/create-contract-manual/create-contract-manual.service';
import { CreateContractService } from '../services/create-contract.service';
import { DeleteContractService } from '../services/delete-contract.service';
import { EditNewContractService } from '../services/editt-new-contract.service';
import { GetContractsByInvestorService } from '../services/get-contract-by-investor.service';
import { GetContractDetailService } from '../services/get-contract-detail.service';
import { GetContractsService } from '../services/get-contracts.service';
import { GetContratAddendumsByIdService } from '../services/get-contrat-addendums-by-id.service';
import { GetOneContractService } from '../services/get-one-contracts.service';
import { ListContractsSuperadminService } from '../services/list-contracts-superadmin.service';
import { RenewContractService } from '../services/renew-contract.service';
import { SendEmailNotificationContract } from '../services/send-notification.service';
import { UploadProofPaymentAddendumService } from '../services/upload-proof-payment-addendum.service';
import { UploadProofPaymentService } from '../services/upload-proof-payment.service';
import { ForceSignatureCheckService } from 'src/apis/ica-contract-service/services/force-signature-check.service';


@ApiTags('Contract')
@Controller('contract')
export class ContractController {
  constructor(
    private readonly createContractService: CreateContractService,
    private readonly createContractManualService: CreateContractManualService,
    private readonly getContractsService: GetContractsService,
    private readonly getOneContractService: GetOneContractService,
    private readonly addSignatoriesService: AddSignatoriesService,
    private readonly renewContractService: RenewContractService,
    private readonly getContractsByInvestorService: GetContractsByInvestorService,
    private readonly uploadProofPaymentService: UploadProofPaymentService,
    private readonly uploadProofPaymentAddendumService: UploadProofPaymentAddendumService,
    private readonly sendNotificationContract: SendEmailNotificationContract,
    private readonly createContractAdditiveService: CreateContractAdditiveService,
    private readonly createContractAdditiveManualService: CreateContractAdditiveManualService,
    private readonly listContractsSuperadminService: ListContractsSuperadminService,
    private readonly getContratAddendumsByIdService: GetContratAddendumsByIdService,
    private readonly getContractDetailService: GetContractDetailService,
    private readonly deleteContractService: DeleteContractService,
    private readonly editNewContractService: EditNewContractService,
    private readonly forceSignatureCheckService: ForceSignatureCheckService,

  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADVISOR, RolesEnum.BROKER, RolesEnum.SUPERADMIN)
  async createContract(
    @Body()
    createContractDto: CreateContractDto,
    @Request() request: IRequestUser,
  ) {
    const token = request.headers['authorization']?.replace('Bearer ', '') || '';
    return this.createContractService.perform(createContractDto, token);
  }

  @ApiOperation({
    summary: 'Rota para criar um novo contrato',
    description: 'Retorna o id do novo contrato criado',
  })
  @ApiOkResponse({
    description: 'id do contrato criado',
    type: GetContractsResponse,
  })
  @ApiBody({ type: CreateNewContractDto })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiBearerAuth('Bearer')
  @Post('manual')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADVISOR, RolesEnum.BROKER, RolesEnum.ADMIN)
  @HttpCode(201)
  async createContractManual(
    @Body()
    createContractDto: CreateNewContractDto,
    @Request() request: IRequestUser,
  ) {
    console.log('----------dto----------');
    console.log(createContractDto);

    const data = await this.createContractManualService.perform(
      createContractDto,
      request.user.id,
    );
    return data;
  }

  @Post('additive')
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADVISOR, RolesEnum.BROKER)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async createContractAdditive(
    @Body()
    createContractDto: CreateContractAdditiveDto,
  ) {
    return this.createContractAdditiveService.perform(createContractDto);
  }

  @Put(':id/edit')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR)
  @UsePipes(new ValidationPipe({ whitelist: true }))
  @ApiOperation({
    summary: 'Rota para editar um contrato',
    description: 'Edita um contrato existente',
  })
  @ApiOkResponse({
    description: 'Contrato editado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiBearerAuth('Bearer')
  @ApiBody({ type: EditNewContractDto })
  async editNewContract(
    @Param() params: EditContractParamsDto,
    @Body()
    editNewContractDto: EditNewContractDto,
  ): Promise<void> {
    return await this.editNewContractService.perform(
      editNewContractDto,
      params.id,
    );
  }



  @Get('list-contracts')
  @UseGuards(JwtAuthGuard)
  @Roles(RolesEnum.ADVISOR, RolesEnum.BROKER)
  @UsePipes(new ValidationPipe({ whitelist: true }))
  @ApiOperation({
    summary:
      'Rota para listar contratos de um usuário com role broker ou advisor',
    description:
      'Retorna uma lista paginada de contratos filtrados pelos parâmetros de consulta informados.',
  })
  @ApiOkResponse({
    description: 'Lista paginada de contratos',
    type: GetContractsResponse,
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiSortingQuery(Object.values(ContractSortFieldEnum))
  @ApiBearerAuth('Bearer')
  async getAllContracts(
    @Query() data: GetContractsDto,
    @SortingParams(Object.values(ContractSortFieldEnum))
    sort?: SortingParam,
  ): Promise<GetContractsResponse> {
    return this.getContractsService.perform({ ...data, sort });
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async getContract(
    @Query()
    query: GetContractDto,
  ) {
    return await this.getOneContractService.perform(query);
  }

  @Post('add-signatories')
  @UseGuards(JwtAuthGuard)
  async addSignatories(
    @Body()
    data: AddSignatariesDto,
  ) {
    return this.addSignatoriesService.perform(data);
  }

  @Patch('renew/:id')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'oldContractPdf', maxCount: 1 },
      { name: 'newContractPdf', maxCount: 1 },
    ]),
  )
  @UseGuards(JwtAuthGuard)
  async renewContract(
    @Param('id') contractId: string,
    @Body() renewContractDto: RenewContractDto,
    @UploadedFiles()
    files: {
      oldContractPdf?: Express.Multer.File[];
      newContractPdf?: Express.Multer.File[];
    },
  ) {
    const oldContractPdfFile = files.oldContractPdf
      ? files.oldContractPdf[0]
      : null;
    const newContractPdfFile = files.newContractPdf
      ? files.newContractPdf[0]
      : null;

    return this.renewContractService.perform(
      contractId,
      renewContractDto,
      oldContractPdfFile,
      newContractPdfFile,
    );
  }

  @Get(':investorId')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN, RolesEnum.BROKER, RolesEnum.ADVISOR)
  async getContracts(@Param('investorId') investorId: string) {
    try {
      const contracts =
        await this.getContractsByInvestorService.perform(investorId);
      return contracts;
    } catch (error) {
      console.error(error);
      throw new HttpException(
        { error: error.response?.data || error.message },
        error.status || error.response?.statusCode || 500,
      );
    }
  }

  @Put('upload/proof-payment')
  @UseInterceptors(FileInterceptor('file'))
  @UseGuards(JwtAuthGuard)
  async uploadProofPayment(
    @Body()
    body: UploadProofPaymentDto,
    @UploadedFile()
    proofPayment: Express.Multer.File,
  ) {
    try {
      return this.uploadProofPaymentService.perform(body, proofPayment);
    } catch (error) {
      throw new HttpException(
        { error: error.response.data || error.message },
        error.status || error.response.statusCode,
      );
    }
  }

  @Post('addendum/proof-payment')
  @UseInterceptors(FileInterceptor('proofPayment'))
  @UseGuards(JwtAuthGuard)
  async uploadProofPaymentAddendum(
    @Body()
    body: UploadProofPaymentAddendumDto,
    @UploadedFile()
    proofPayment: Express.Multer.File,
  ) {
    try {
      return this.uploadProofPaymentAddendumService.perform(body, proofPayment);
    } catch (error) {
      throw new HttpException(
        { error: error.response.data || error.message },
        error.status || error.response.statusCode,
      );
    }
  }

  @Post('send-notification/:contractId')
  @UseGuards(JwtAuthGuard)
  async sendNotification(@Param('contractId') contractId: string) {
    try {
      return this.sendNotificationContract.perform(contractId);
    } catch (error) {
      throw new HttpException(
        { error: error.response.data || error.message },
        error.status || error.response.statusCode,
      );
    }
  }

  @Post('additive-manual')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'contractPdf', maxCount: 1 },
      { name: 'proofPayment', maxCount: 1 },
    ]),
  )
  async createContractAdditiveManual(
    @UploadedFiles()
    files: {
      contractPdf?: Express.Multer.File[];
      proofPayment?: Express.Multer.File[];
    },
    @Body()
    createContractDto: CreateContractAdditiveManualDto,
  ) {
    const contractPdfFile = files.contractPdf ? files.contractPdf[0] : null;
    const proofPaymentFile = files.proofPayment ? files.proofPayment[0] : null;
    return this.createContractAdditiveManualService.perform(createContractDto, [
      contractPdfFile,
      proofPaymentFile,
    ]);
  }

  @Get('list-contracts/superadmin')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN)
  @UsePipes(new ValidationPipe({ whitelist: true }))
  @ApiOperation({
    summary: 'Rota para listar contratos de um usuário com role superadmin',
    description:
      'Retorna uma lista paginada de contratos filtrados pelos parâmetros de consulta informados.',
  })
  @ApiOkResponse({
    description: 'Lista paginada de contratos',
    type: GetContractsResponse,
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiBearerAuth('Bearer')
  @ApiSortingQuery(Object.values(ContractSortFieldEnum))
  async listContractsSuperadmin(
    @Query()
    data: ListContractsSuperadminDto,
    @SortingParams(Object.values(ContractSortFieldEnum))
    sort?: SortingParam,
  ) {
    return await this.listContractsSuperadminService.perform({ ...data, sort });
  }

  @Get(':id/addendum')
  @UseGuards(JwtAuthGuard)
  async getContractAddendums(@Param('id') id: string) {
    return this.getContratAddendumsByIdService.execute(id);
  }

  @Get('get-detail/:id')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADVISOR, RolesEnum.BROKER)
  async getContractDetail(@Param('id') id: string) {
    return this.getContractDetailService.perform(id);
  }

  @Post(':id/delete')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.SUPERADMIN)
  @ApiOperation({
    summary: 'Rota para deletar um contrato',
    description:
      'Permite o deletamento de contratos nos seguintes status: rascunho, aguardando auditoria, rejeitado, expirado por auditoria, expirado por investidor ou expirado por falha de prova de pagamento (Super Admin). Broker pode deletar apenas contratos em rascunho.',
  })
  @ApiOkResponse({
    description: 'Contrato deletado com sucesso',
    type: DeleteContractResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Contrato não pode ser deletado ou já foi deletado',
  })
  @ApiResponse({
    status: 401,
    description: 'Usuário não autorizado para deletar contratos',
  })
  @ApiResponse({
    status: 404,
    description: 'Contrato não encontrado',
  })
  @ApiBearerAuth('Bearer')
  @ApiBody({ type: DeleteContractDto })
  async deleteContract(
    @Body() body: DeleteContractDto,
    @Request() req: Request & IRequestUser,
    @Param() params: ContractIdParamDto,
  ) {
    return await this.deleteContractService.perform(
      body,
      req.user.id,
      params.id,
    );
  }

  @Post(':id/force-signature-check')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.SUPERADMIN)
  @ApiOperation({
    summary: 'Força verificação do status de assinatura de um contrato',
    description: 'Verifica imediatamente o status de assinatura no serviço externo e atualiza o contrato se necessário',
  })
  @ApiOkResponse({
    description: 'Status de assinatura verificado e atualizado com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiBearerAuth('Bearer')
  async forceSignatureCheck(@Param('id') contractId: string) {
    return await this.forceSignatureCheckService.perform(contractId);
  }
}
