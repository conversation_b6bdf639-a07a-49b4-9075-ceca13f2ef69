{"version": 3, "file": "force-signature-check.service.js", "sourceRoot": "/", "sources": ["apis/ica-contract-service/services/force-signature-check.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,iCAAyD;AACzD,2CAAgF;AASzE,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IACrC,KAAK,CAAC,OAAO,CAAC,UAAkB;QAC9B,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,wBAAwB,cAAc,UAAU,wBAAwB,CAAC;YAEpG,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAAC;YACzD,OAAO,CAAC,GAAG,CAAC,8CAA8C,EAAE,UAAU,CAAC,CAAC;YAExE,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAG/B,GAAG,EAAE,EAAE,EAAE;gBACT,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;YACvE,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAE9D,IAAI,KAAK,YAAY,kBAAU,EAAE,CAAC;gBAChC,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC;oBACjE,MAAM,IAAI,4BAAmB,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACrD,CAAC;gBAED,MAAM,IAAI,sBAAa,CACrB,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,IAAI,iCAAiC,EAClE,KAAK,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,CAC9B,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,sBAAa,CACrB,iCAAiC,EACjC,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAxCY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;GACA,0BAA0B,CAwCtC", "sourcesContent": ["import axios, { AxiosError, AxiosResponse } from 'axios';\nimport { Injectable, HttpException, BadRequestException } from '@nestjs/common';\n\ninterface IForceSignatureCheckResponse {\n  contractId: string;\n  status: string;\n  message: string;\n}\n\n@Injectable()\nexport class ForceSignatureCheckService {\n  async perform(contractId: string): Promise<IForceSignatureCheckResponse> {\n    try {\n      const url = `${process.env.API_CONTRACT_SERVICE_URL}/contracts/${contractId}/force-signature-check`;\n\n      console.log('🔍 ForceSignatureCheckService - URL:', url);\n      console.log('🔍 ForceSignatureCheckService - Contract ID:', contractId);\n\n      const response = await axios.post<\n        {},\n        AxiosResponse<IForceSignatureCheckResponse>\n      >(url, {}, {\n        timeout: 30000,\n        headers: {\n          'Content-Type': 'application/json',\n        }\n      });\n\n      console.log('✅ ForceSignatureCheckService - Response:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ ForceSignatureCheckService - Error:', error);\n      \n      if (error instanceof AxiosError) {\n        if (error.response?.status > 399 && error.response?.status < 499) {\n          throw new BadRequestException(error.response.data);\n        }\n\n        throw new HttpException(\n          error.response?.data?.message || 'Error checking signature status',\n          error.response?.status || 500,\n        );\n      }\n      \n      throw new HttpException(\n        'Error checking signature status',\n        500,\n      );\n    }\n  }\n}\n"]}