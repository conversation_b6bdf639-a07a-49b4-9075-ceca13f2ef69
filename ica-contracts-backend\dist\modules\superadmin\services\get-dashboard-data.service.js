"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetDashboardDataService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
const typeorm_2 = require("typeorm");
let GetDashboardDataService = class GetDashboardDataService {
    constructor(contractRepository, ownerRoleRelationEntity) {
        this.contractRepository = contractRepository;
        this.ownerRoleRelationEntity = ownerRoleRelationEntity;
    }
    async perform() {
        const contracts = await this.contractRepository.find({
            where: { status: contract_status_enum_1.ContractStatusEnum.ACTIVE },
            relations: {
                ownerRoleRelation: { business: true, owner: true },
                signataries: true,
                investor: true,
                addendum: true,
            },
        });
        const [brokers, numberBrokers] = await this.ownerRoleRelationEntity.findAndCount({
            relations: {
                role: true,
            },
            where: {
                role: { name: 'broker' },
            },
        });
        let totalAddendum = 0;
        const [adivors, numberAdvisors] = await this.ownerRoleRelationEntity.findAndCount({
            relations: {
                role: true,
            },
            where: {
                role: { name: 'advisor' },
            },
        });
        console.log(`total sem filtro ${contracts.length}`);
        const activeContracts = contracts.filter((c) => this.isContractDateValid(c.startContract, c.endContract));
        console.log(`total com filtro ${activeContracts.length}`);
        let p2pContractAmount = 0;
        let scpContractAmount = 0;
        let uniqueInvestors = 0;
        let p2pContractNumber = 0;
        let scpContractNumber = 0;
        let totalContracts = activeContracts.length;
        let activeQuotes = 0;
        activeContracts.forEach((contract) => {
            contract.signataries.forEach((signatory) => {
                const investmentValue = Number(signatory.investmentValue) || 0;
                if (['mutuo', 'MUTUO'].includes(contract.type)) {
                    p2pContractAmount += investmentValue;
                    p2pContractNumber++;
                    return;
                }
                if (['scp', 'SCP'].includes(contract.type)) {
                    scpContractAmount += investmentValue;
                    scpContractNumber++;
                    uniqueInvestors++;
                }
            });
            contract.addendum.forEach((addendum) => {
                if (addendum.status === 'FULLY_SIGNED' && this.isAddendumNotExpired(addendum.expiresIn)) {
                    const addendumValue = Number(addendum.value) || 0;
                    totalContracts++;
                    totalAddendum++;
                    if (['mutuo', 'MUTUO'].includes(contract.type)) {
                        p2pContractAmount += addendumValue;
                        p2pContractNumber++;
                        return;
                    }
                    if (['scp', 'SCP'].includes(contract.type)) {
                        scpContractAmount += addendumValue;
                        scpContractNumber++;
                    }
                }
            });
        });
        activeQuotes = Math.round(scpContractAmount / 5000);
        return {
            distributedIncome: 1,
            scpWithdraws: 0,
            p2pWithdraws: 0,
            numberBrokers,
            numberAdvisors,
            p2pContractNumber,
            scpContractNumber,
            p2pContractAmount: p2pContractAmount || 0,
            scpContractAmount: scpContractAmount || 0,
            activeQuotes: activeQuotes || 0,
            shareholder: uniqueInvestors,
            activeInvestorsNumber: totalContracts,
        };
    }
    isContractDateValid(startContract, endContract) {
        const currentDate = new Date();
        const startDate = new Date(startContract);
        const endDate = new Date(endContract);
        if (Number.isNaN(startDate.getTime()) || Number.isNaN(endDate.getTime())) {
            return false;
        }
        if (startDate > currentDate) {
            return false;
        }
        if (endDate <= startDate) {
            return false;
        }
        return true;
    }
    isAddendumNotExpired(expiresIn) {
        if (!expiresIn)
            return false;
        const today = new Date();
        const expires = new Date(expiresIn);
        return expires >= today;
    }
};
exports.GetDashboardDataService = GetDashboardDataService;
exports.GetDashboardDataService = GetDashboardDataService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], GetDashboardDataService);
//# sourceMappingURL=get-dashboard-data.service.js.map