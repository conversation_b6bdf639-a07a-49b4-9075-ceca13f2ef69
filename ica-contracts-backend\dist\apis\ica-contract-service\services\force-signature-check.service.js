"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ForceSignatureCheckService = void 0;
const axios_1 = require("axios");
const common_1 = require("@nestjs/common");
let ForceSignatureCheckService = class ForceSignatureCheckService {
    async perform(contractId) {
        try {
            const url = `${process.env.API_CONTRACT_SERVICE_URL}/contracts/${contractId}/force-signature-check`;
            console.log('🔍 ForceSignatureCheckService - URL:', url);
            console.log('🔍 ForceSignatureCheckService - Contract ID:', contractId);
            const response = await axios_1.default.post(url, {}, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            console.log('✅ ForceSignatureCheckService - Response:', response.data);
            return response.data;
        }
        catch (error) {
            console.error('❌ ForceSignatureCheckService - Error:', error);
            if (error instanceof axios_1.AxiosError) {
                if (error.response?.status > 399 && error.response?.status < 499) {
                    throw new common_1.BadRequestException(error.response.data);
                }
                throw new common_1.HttpException(error.response?.data?.message || 'Error checking signature status', error.response?.status || 500);
            }
            throw new common_1.HttpException('Error checking signature status', 500);
        }
    }
};
exports.ForceSignatureCheckService = ForceSignatureCheckService;
exports.ForceSignatureCheckService = ForceSignatureCheckService = __decorate([
    (0, common_1.Injectable)()
], ForceSignatureCheckService);
//# sourceMappingURL=force-signature-check.service.js.map