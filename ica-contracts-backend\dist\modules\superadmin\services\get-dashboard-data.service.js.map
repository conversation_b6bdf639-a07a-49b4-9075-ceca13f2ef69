{"version": 3, "file": "get-dashboard-data.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/get-dashboard-data.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,+FAAsF;AACtF,qHAA0G;AAC1G,qFAA2E;AAC3E,qCAAqC;AAG9B,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAClC,YAEU,kBAA8C,EAE9C,uBAA4D;QAF5D,uBAAkB,GAAlB,kBAAkB,CAA4B;QAE9C,4BAAuB,GAAvB,uBAAuB,CAAqC;IACnE,CAAC;IAEJ,KAAK,CAAC,OAAO;QAEX,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACnD,KAAK,EAAE,EAAE,MAAM,EAAE,yCAAkB,CAAC,MAAM,EAAE;YAC5C,SAAS,EAAE;gBACT,iBAAiB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;gBAClD,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,MAAM,CAAC,OAAO,EAAE,aAAa,CAAC,GAC5B,MAAM,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;YAC9C,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aACzB;SACF,CAAC,CAAC;QACH,IAAI,aAAa,GAAG,CAAC,CAAC;QACxB,MAAM,CAAC,OAAO,EAAE,cAAc,CAAC,GAC7B,MAAM,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;YAC9C,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;aAC1B;SACF,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAEtD,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAC7C,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,WAAW,CAAC,CACzD,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,oBAAoB,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;QAE1D,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC;QAC5C,IAAI,YAAY,GAAG,CAAC,CAAC;QAGrB,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAEnC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;gBACzC,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAE/D,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/C,iBAAiB,IAAI,eAAe,CAAC;oBACrC,iBAAiB,EAAE,CAAC;oBACpB,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3C,iBAAiB,IAAI,eAAe,CAAC;oBACrC,iBAAiB,EAAE,CAAC;oBACpB,eAAe,EAAG,CAAC;gBACrB,CAAC;YACH,CAAC,CAAC,CAAC;YAGH,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAErC,IAAI,QAAQ,CAAC,MAAM,KAAK,cAAc,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBACxF,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAClD,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAG,CAAC;oBAEjB,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC/C,iBAAiB,IAAI,aAAa,CAAC;wBACnC,iBAAiB,EAAE,CAAC;wBAEpB,OAAO;oBACT,CAAC;oBACD,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC3C,iBAAiB,IAAI,aAAa,CAAC;wBACnC,iBAAiB,EAAE,CAAC;oBAEtB,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAC;QACpD,OAAO;YACL,iBAAiB,EAAE,CAAC;YACpB,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;YACf,aAAa;YACb,cAAc;YACd,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB,EAAE,iBAAiB,IAAI,CAAC;YACzC,iBAAiB,EAAE,iBAAiB,IAAI,CAAC;YACzC,YAAY,EAAE,YAAY,IAAI,CAAC;YAC/B,WAAW,EAAE,eAAe;YAC5B,qBAAqB,EAAE,cAAc;SACtC,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,aAAmB,EAAE,WAAiB;QAChE,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC;QAEtC,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,SAAS,GAAG,WAAW,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,OAAO,IAAI,SAAS,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,oBAAoB,CAAC,SAAiB;QAC5C,IAAI,CAAC,SAAS;YAAE,OAAO,KAAK,CAAC;QAC7B,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,OAAO,OAAO,IAAI,KAAK,CAAC;IAC1B,CAAC;CACF,CAAA;AA1IY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCADd,oBAAU;QAEL,oBAAU;GALlC,uBAAuB,CA0InC", "sourcesContent": ["import { ConflictException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\n@Injectable()\r\nexport class GetDashboardDataService {\r\n  constructor(\r\n    @InjectRepository(ContractEntity)\r\n    private contractRepository: Repository<ContractEntity>,\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private ownerRoleRelationEntity: Repository<OwnerRoleRelationEntity>,\r\n  ) {}\r\n\r\n  async perform() {\r\n    // Buscar todos os contratos ativos (super admin vê todos)\r\n    const contracts = await this.contractRepository.find({\r\n      where: { status: ContractStatusEnum.ACTIVE },\r\n      relations: {\r\n        ownerRoleRelation: { business: true, owner: true },\r\n        signataries: true,\r\n        investor: true,\r\n        addendum: true,\r\n      },\r\n    });\r\n\r\n    const [brokers, numberBrokers] =\r\n      await this.ownerRoleRelationEntity.findAndCount({\r\n        relations: {\r\n          role: true,\r\n        },\r\n        where: {\r\n          role: { name: 'broker' },\r\n        },\r\n      });\r\n      let totalAddendum = 0;\r\n    const [adivors, numberAdvisors] =\r\n      await this.ownerRoleRelationEntity.findAndCount({\r\n        relations: {\r\n          role: true,\r\n        },\r\n        where: {\r\n          role: { name: 'advisor' },\r\n        },\r\n      });\r\n      console.log(`total sem filtro ${contracts.length}`);\r\n    // Filtrar apenas contratos com datas válidas\r\n    const activeContracts = contracts.filter((c) =>\r\n      this.isContractDateValid(c.startContract, c.endContract),\r\n    );\r\n    console.log(`total com filtro ${activeContracts.length}`);\r\n\r\n    let p2pContractAmount = 0;\r\n    let scpContractAmount = 0;\r\n    let uniqueInvestors = 0;\r\n    let p2pContractNumber = 0;\r\n    let scpContractNumber = 0;\r\n    let totalContracts = activeContracts.length;\r\n    let activeQuotes = 0;\r\n\r\n    // Calcular valores e contadores baseados nos contratos e adendos\r\n    activeContracts.forEach((contract) => {\r\n      // Contar contratos principais\r\n      contract.signataries.forEach((signatory) => {\r\n        const investmentValue = Number(signatory.investmentValue) || 0;\r\n        \r\n        if (['mutuo', 'MUTUO'].includes(contract.type)) {\r\n          p2pContractAmount += investmentValue;\r\n          p2pContractNumber++;\r\n          return;\r\n        }\r\n\r\n        if (['scp', 'SCP'].includes(contract.type)) {\r\n          scpContractAmount += investmentValue;\r\n          scpContractNumber++;\r\n          uniqueInvestors ++;\r\n        }\r\n      });\r\n      // Contar aditivos assinados - Aditivos\r\n     \r\n      contract.addendum.forEach((addendum) => {\r\n  \r\n        if (addendum.status === 'FULLY_SIGNED' && this.isAddendumNotExpired(addendum.expiresIn)) {\r\n          const addendumValue = Number(addendum.value) || 0;\r\n          totalContracts++;\r\n          totalAddendum ++;\r\n          \r\n          if (['mutuo', 'MUTUO'].includes(contract.type)) {\r\n            p2pContractAmount += addendumValue;\r\n            p2pContractNumber++;\r\n          \r\n            return;\r\n          }\r\n          if (['scp', 'SCP'].includes(contract.type)) {\r\n            scpContractAmount += addendumValue;\r\n            scpContractNumber++;\r\n          \r\n          }\r\n        }\r\n      });\r\n    });\r\n    activeQuotes = Math.round(scpContractAmount / 5000);\r\n    return {\r\n      distributedIncome: 1,\r\n      scpWithdraws: 0,\r\n      p2pWithdraws: 0,\r\n      numberBrokers,\r\n      numberAdvisors,\r\n      p2pContractNumber,\r\n      scpContractNumber,\r\n      p2pContractAmount: p2pContractAmount || 0,\r\n      scpContractAmount: scpContractAmount || 0,\r\n      activeQuotes: activeQuotes || 0,\r\n      shareholder: uniqueInvestors,\r\n      activeInvestorsNumber: totalContracts,\r\n    };\r\n  }\r\n\r\n  private isContractDateValid(startContract: Date, endContract: Date): boolean {\r\n    const currentDate = new Date();\r\n    const startDate = new Date(startContract);\r\n    const endDate = new Date(endContract);\r\n\r\n    if (Number.isNaN(startDate.getTime()) || Number.isNaN(endDate.getTime())) {\r\n      return false;\r\n    }\r\n\r\n    if (startDate > currentDate) {\r\n      return false;\r\n    }\r\n\r\n    if (endDate <= startDate) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private isAddendumNotExpired(expiresIn: string): boolean {\r\n    if (!expiresIn) return false;\r\n    const today = new Date();\r\n    const expires = new Date(expiresIn);\r\n    return expires >= today;\r\n  }\r\n}\r\n"]}