"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IcaContractServiceModule = void 0;
const cache_manager_1 = require("@nestjs/cache-manager");
const common_1 = require("@nestjs/common");
const create_existing_contract_service_1 = require("./services/create-existing-contract.service");
const create_new_contract_service_1 = require("./services/create-new-contract.service");
const delete_contract_service_1 = require("./services/delete-contract.service");
const details_report_investor_service_1 = require("./services/details-report-investor.service");
const force_signature_check_service_1 = require("./services/force-signature-check.service");
const generate_report_investors_service_1 = require("./services/generate-report-investors.service");
const request_investor_credentials_service_1 = require("./services/request-investor-credentials.service");
const resubmit_rejected_contract_service_1 = require("./services/resubmit-rejected-contract.service");
let IcaContractServiceModule = class IcaContractServiceModule {
};
exports.IcaContractServiceModule = IcaContractServiceModule;
exports.IcaContractServiceModule = IcaContractServiceModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [cache_manager_1.CacheModule.register()],
        providers: [
            generate_report_investors_service_1.GenerateReportInvestorsService,
            details_report_investor_service_1.DetailsReportInvestorService,
            create_new_contract_service_1.CreateNewContractService,
            create_existing_contract_service_1.CreateExistingContractApiService,
            resubmit_rejected_contract_service_1.ResubmitRejectedContractApiService,
            request_investor_credentials_service_1.RequestInvestorCredentialsService,
            delete_contract_service_1.DeleteContractApiService,
            force_signature_check_service_1.ForceSignatureCheckService,
        ],
        exports: [
            generate_report_investors_service_1.GenerateReportInvestorsService,
            details_report_investor_service_1.DetailsReportInvestorService,
            create_new_contract_service_1.CreateNewContractService,
            create_existing_contract_service_1.CreateExistingContractApiService,
            resubmit_rejected_contract_service_1.ResubmitRejectedContractApiService,
            request_investor_credentials_service_1.RequestInvestorCredentialsService,
            delete_contract_service_1.DeleteContractApiService,
            force_signature_check_service_1.ForceSignatureCheckService,
        ],
    })
], IcaContractServiceModule);
//# sourceMappingURL=ica-contract-service.module.js.map